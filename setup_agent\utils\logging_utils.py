"""
Centralized logging utilities with proper configuration.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any, List

from ..core.config import config


def setup_logging(
    log_level: Optional[str] = None,
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    format_string: Optional[str] = None
) -> None:
    """Setup centralized logging configuration."""
    
    # Get configuration
    log_level = log_level or config.get(['logging', 'level'], 'INFO')
    log_file = log_file or config.get(['logging', 'file'], 'agent.log')
    
    # Default format
    if format_string is None:
        format_string = '%(asctime)s [%(levelname)8s] %(name)s: %(message)s'
    
    # Create formatter
    formatter = logging.Formatter(format_string)
    
    # Get root logger
    root_logger = logging.getLogger()
    
    # Clear existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Set log level
    try:
        level = getattr(logging, log_level.upper())
        root_logger.setLevel(level)
    except AttributeError:
        root_logger.setLevel(logging.INFO)
        print(f"Warning: Invalid log level '{log_level}', using INFO")
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)  # Console shows INFO and above
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        try:
            # Ensure log directory exists
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)  # File gets all messages
            root_logger.addHandler(file_handler)
            
        except Exception as e:
            print(f"Warning: Could not setup file logging: {e}")
    
    # Suppress noisy third-party loggers
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    
    # Log the setup
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured: level={log_level}, file={log_file}")


def get_logger(name: str) -> logging.Logger:
    """Get a logger with consistent configuration."""
    return logging.getLogger(name)


class StructuredLogger:
    """Logger with structured logging support."""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_structured(
        self, 
        level: int, 
        message: str, 
        **kwargs: Any
    ) -> None:
        """Log with structured data."""
        if kwargs:
            # Format structured data
            structured_data = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
            full_message = f"{message} | {structured_data}"
        else:
            full_message = message
        
        self.logger.log(level, full_message)
    
    def info(self, message: str, **kwargs: Any) -> None:
        """Log info with structured data."""
        self.log_structured(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs: Any) -> None:
        """Log warning with structured data."""
        self.log_structured(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs: Any) -> None:
        """Log error with structured data."""
        self.log_structured(logging.ERROR, message, **kwargs)
    
    def debug(self, message: str, **kwargs: Any) -> None:
        """Log debug with structured data."""
        self.log_structured(logging.DEBUG, message, **kwargs)


class LogCapture:
    """Capture logs for testing or analysis."""
    
    def __init__(self, logger_name: Optional[str] = None):
        self.logger_name = logger_name
        self.captured_logs: List[Dict[str, Any]] = []
        self.handler = None
    
    def __enter__(self):
        # Create custom handler
        self.handler = logging.Handler()
        self.handler.emit = self._capture_log
        
        # Add to logger
        if self.logger_name:
            logger = logging.getLogger(self.logger_name)
        else:
            logger = logging.getLogger()
        
        logger.addHandler(self.handler)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Remove handler
        if self.logger_name:
            logger = logging.getLogger(self.logger_name)
        else:
            logger = logging.getLogger()
        
        if self.handler:
            logger.removeHandler(self.handler)
    
    def _capture_log(self, record):
        """Capture log record."""
        self.captured_logs.append({
            'level': record.levelname,
            'message': record.getMessage(),
            'timestamp': record.created,
            'logger': record.name
        })
    
    def get_logs(self, level: Optional[str] = None) -> list:
        """Get captured logs, optionally filtered by level."""
        if level:
            return [log for log in self.captured_logs if log['level'] == level.upper()]
        return self.captured_logs.copy()
    
    def clear(self) -> None:
        """Clear captured logs."""
        self.captured_logs.clear()


def configure_module_logging(modules: Dict[str, str]) -> None:
    """Configure logging levels for specific modules."""
    for module_name, level in modules.items():
        try:
            log_level = getattr(logging, level.upper())
            logging.getLogger(module_name).setLevel(log_level)
        except AttributeError:
            print(f"Warning: Invalid log level '{level}' for module '{module_name}'")


def log_performance(func):
    """Decorator to log function performance."""
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"{func.__name__} completed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    return wrapper


# Initialize logging on import
if not logging.getLogger().handlers:
    setup_logging()
